const SecureConfig = {
  getFunctionUrl(functionName = 'InvitationService') {
    const baseUrl = window.appConfig?.functionUrl;
    if (!baseUrl || baseUrl.includes('ERROR_MISSING')) {
      return null;
    }
    return `${baseUrl}/api/${functionName}`;
  },

  getFunctionKey() {
    const functionKey = window.appConfig?.invitationFunctionKey;
    if (!functionKey || functionKey.includes('ERROR_MISSING')) {
      return null;
    }
    return functionKey;
  },

  buildSecureUrl(functionName, operation) {
    const baseUrl = this.getFunctionUrl(functionName);
    const functionKey = this.getFunctionKey();
    if (!baseUrl || !functionKey) {
      return null;
    }
    return `${baseUrl}?operation=${operation}&code=${functionKey}`;
  }
};

let currentInvitationData = null;

function initializeConfiguration() {
  const functionUrl = window.appConfig?.functionUrl;
  if (!functionUrl || functionUrl.includes('ERROR_MISSING')) {
    showMessage('Configuration error: Azure Function URL not set', true);
  }
}

const InputSanitizer = {
  sanitizeString(input) {
    if (typeof input !== 'string') return '';
    return input.trim().replace(/[<>\"'&]/g, '').substring(0, 256);
  },

  validateEmail(email) {
    if (!email || typeof email !== 'string') return false;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
  },

  validateName(name) {
    if (!name || typeof name !== 'string') return false;
    const trimmed = name.trim();
    return trimmed.length >= 1 && trimmed.length <= 50 && /^[a-zA-Z\s'-]+$/.test(trimmed);
  }
};

// DOM Elements
const errorMessage = $('#errorMessage');
const successMessage = $('#successMessage');
const invitationForm = $('#invitationForm');
const sendButton = $('#sendButton');
const recentInvitations = $('#recentInvitations');

function showMessage(message, isError = true) {
  errorMessage.addClass('d-none');
  successMessage.addClass('d-none');
  
  if (isError) {
    $('#errorText').text(message);
    errorMessage.removeClass('d-none');
  } else {
    $('#successText').text(message);
    successMessage.removeClass('d-none');
  }
}

function showLoadingState(message) {
  sendButton.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> ' + message);
}

function resetLoadingState() {
  sendButton.prop('disabled', false).html('<i class="fas fa-paper-plane me-2"></i>Send Invitation');
}

async function sendInvitation(invitationData) {
  showLoadingState('Sending Invitation...');

  try {
    const sanitizedData = {
      email: InputSanitizer.sanitizeString(invitationData.email),
      firstName: InputSanitizer.sanitizeString(invitationData.firstName),
      lastName: InputSanitizer.sanitizeString(invitationData.lastName)
    };

    if (!InputSanitizer.validateEmail(sanitizedData.email)) {
      throw new Error('Invalid email format');
    }

    if (!InputSanitizer.validateName(sanitizedData.firstName)) {
      throw new Error('Invalid first name');
    }

    if (!InputSanitizer.validateName(sanitizedData.lastName)) {
      throw new Error('Invalid last name');
    }

    const secureUrl = SecureConfig.buildSecureUrl('InvitationService', 'invite-user');
    if (!secureUrl) {
      throw new Error('Azure Function configuration missing. Please check Power Pages settings.');
    }

    const requestBody = {
      Email: sanitizedData.email,
      FirstName: sanitizedData.firstName,
      LastName: sanitizedData.lastName,
      ApplicationName: window.appConfig?.applicationName
    };

    const response = await fetch(secureUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Version': '3.0.0-simplified',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify(requestBody)
    });

    if (response.status === 429) {
      throw new Error('Too many requests. Please wait a moment before trying again.');
    }

    if (!response.ok) {
      let errorDetails = 'No additional details';
      try {
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const errorData = await response.json();
          errorDetails = errorData.message || errorData.error || JSON.stringify(errorData);
        } else {
          const textResponse = await response.text();
          errorDetails = textResponse || 'Empty response';
        }
      } catch (parseError) {
        // Ignore parse errors
      }
      throw new Error(`Server error: ${response.status} - ${errorDetails}`);
    }

    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      throw new Error(`Server error: ${response.status} - Expected JSON response`);
    }

    const result = await response.json();

    let success, message, errorCode, retryAfter;

    if (result.data) {
      success = result.data.success;
      message = result.data.message;
      errorCode = result.data.errorCode;
      retryAfter = result.data.retryAfter;
    } else {
      success = result.success;
      message = result.message;
      errorCode = result.errorCode;
      retryAfter = result.retryAfter;
    }

    const isSuccess = response.ok && success === true;

    if (!isSuccess) {
      if (response.status === 429 || errorCode === 'RateLimitExceeded') {
        const retryMessage = retryAfter ? ` Please try again after ${new Date(retryAfter).toLocaleTimeString()}.` : ' Please try again later.';
        throw new Error((message || 'Rate limit exceeded.') + retryMessage);
      }
      throw new Error(message || 'Failed to send invitation');
    }

    return {
      success: true,
      message: message || 'Invitation sent successfully',
      correlationId: result.correlationId
    };
  } catch (error) {
    throw error;
  } finally {
    resetLoadingState();
  }
}

function validateForm() {
  let isValid = true;
  
  $('.form-control').removeClass('is-invalid');
  $('.invalid-feedback').text('');

  const email = $('#email').val();
  if (!InputSanitizer.validateEmail(email)) {
    $('#emailError').text('Please enter a valid email address');
    $('#email').addClass('is-invalid');
    isValid = false;
  }

  const firstName = $('#firstName').val();
  if (!InputSanitizer.validateName(firstName)) {
    $('#firstNameError').text('Please enter a valid first name (letters, spaces, hyphens, and apostrophes only)');
    $('#firstName').addClass('is-invalid');
    isValid = false;
  }

  const lastName = $('#lastName').val();
  if (!InputSanitizer.validateName(lastName)) {
    $('#lastNameError').text('Please enter a valid last name (letters, spaces, hyphens, and apostrophes only)');
    $('#lastName').addClass('is-invalid');
    isValid = false;
  }

  return isValid;
}

function addToRecentInvitations(email, firstName, lastName) {
  const timestamp = new Date().toLocaleString();
  const invitationHtml = `
    <div class="d-flex justify-content-between align-items-center py-1">
      <span><strong>${firstName} ${lastName}</strong> (${email})</span>
      <small class="text-muted">${timestamp}</small>
    </div>
  `;
  
  if (recentInvitations.text().includes('No recent invitations')) {
    recentInvitations.html(invitationHtml);
  } else {
    recentInvitations.prepend(invitationHtml);
  }
  
  // Keep only the last 5 invitations
  const invitations = recentInvitations.children();
  if (invitations.length > 5) {
    invitations.slice(5).remove();
  }
}

function initializeFormHandlers() {
  invitationForm.submit(async function(event) {
    event.preventDefault();
    showMessage('', false);

    try {
      if (!validateForm()) {
        return;
      }

      const invitationData = {
        email: $('#email').val(),
        firstName: $('#firstName').val(),
        lastName: $('#lastName').val()
      };

      // Store current invitation data for potential overwrite
      currentInvitationData = invitationData;

      const result = await sendInvitation(invitationData);

      if (result.success) {
        showMessage(`Invitation sent successfully to ${invitationData.email}!`, false);
        addToRecentInvitations(invitationData.email, invitationData.firstName, invitationData.lastName);
        invitationForm[0].reset();
        currentInvitationData = null;
      }

    } catch (error) {
      showMessage(error.message || "An unexpected error occurred. Please try again.");
    }
  });
}



$(document).ready(function() {
  initializeConfiguration();
  initializeFormHandlers();
});
