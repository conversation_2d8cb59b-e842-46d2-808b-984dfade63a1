const SecureConfig = {
  getFunctionUrl(functionName = 'PasswordService') {
    const baseUrl = window.appConfig?.functionUrl;
    if (!baseUrl) {
      return null;
    }
    return `${baseUrl}/api/${functionName}`;
  },

  getFunctionKey() {
    const functionKey = window.appConfig?.passwordFunctionKey;
    if (!functionKey || functionKey.includes('ERROR_MISSING')) {
      return null;
    }
    return functionKey;
  },

  buildSecureUrl(functionName, operation) {
    const baseUrl = this.getFunctionUrl(functionName);
    const functionKey = this.getFunctionKey();

    if (!baseUrl || !functionKey) {
      return null;
    }

    return `${baseUrl}?operation=${operation}&code=${functionKey}`;
  }
};

const PASSWORD_SERVICE_URL = SecureConfig.getFunctionUrl('PasswordService');
const APPLICATION_NAME = window.appConfig?.applicationName || "";

// This page is ONLY for token-based password resets
const urlParams = new URLSearchParams(window.location.search);
const RESET_TOKEN = urlParams.get('token');

// Debug logging for URL and token extraction
console.log('🔍 Reset Password Page Debug:', {
  currentUrl: window.location.href,
  searchParams: window.location.search,
  extractedToken: RESET_TOKEN ? 'Token found (length: ' + RESET_TOKEN.length + ')' : 'No token found',
  allParams: Object.fromEntries(urlParams.entries())
});

if (!PASSWORD_SERVICE_URL) {
  console.error("PasswordService URL not configured");
}

// Validate that we have a reset token
if (!RESET_TOKEN) {
  console.error('❌ No reset token found in URL. Expected format: ?token=<reset-token>');
  $(document).ready(function() {
    showMessage("Invalid reset link. Please request a new password reset.", true);
    $('#submitButton').prop('disabled', true);
  });
} else {
  console.log('✅ Reset token found and ready for password reset');
}

const InputSanitizer = {
  sanitizeInput(input) {
    if (typeof input !== 'string') return '';
    return input.trim().replace(/[<>\"'&]/g, '').substring(0, 256);
  },

  validatePassword(password) {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar;
  }
};

// DOM Elements - Only for password reset with verification code
const errorMessageDiv = $('#errorMessage');
const successMessageDiv = $('#successMessage');
const submitButton = $('#submitButton');
const passwordForm = $('#passwordForm');
const verificationCodeInput = $('#verificationCode');
const newPasswordInput = $('#newPassword');
const confirmPasswordInput = $('#confirmPassword');
const toggleButtons = {
  newPassword: $('#toggleNewPassword'),
  confirmPassword: $('#toggleConfirmPassword')
};



// UI Functions - Same pattern as registration.js
function showMessage(message, isError = true, timeout = 0) {
  errorMessageDiv.hide();
  successMessageDiv.hide();
  const messageDiv = isError ? errorMessageDiv : successMessageDiv;
  messageDiv.html(message).show();
  if (timeout > 0) {
    setTimeout(() => messageDiv.fadeOut(), timeout);
  }
}

function clearMessages() {
  errorMessageDiv.hide();
  successMessageDiv.hide();
}

// Success notification popup
function showSuccessNotification(title, message) {
  // Create modal HTML
  const modalHtml = `
    <div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header bg-success text-white">
            <h5 class="modal-title" id="successModalLabel">
              <i class="fas fa-check-circle me-2"></i>${title}
            </h5>
          </div>
          <div class="modal-body text-center">
            <div class="mb-3">
              <i class="fas fa-check-circle text-success" style="font-size: 3rem;"></i>
            </div>
            <p class="mb-0">${message}</p>
            <p class="text-muted mt-2">You will be redirected shortly...</p>
          </div>
        </div>
      </div>
    </div>
  `;

  // Remove any existing modal
  $('#successModal').remove();
  
  // Add modal to body
  $('body').append(modalHtml);
  
  // Show modal
  const modal = new bootstrap.Modal(document.getElementById('successModal'), {
    backdrop: 'static',
    keyboard: false
  });
  modal.show();
  
  // Auto-hide after 2.5 seconds
  setTimeout(() => {
    modal.hide();
  }, 2500);
}

// Password visibility toggle - Same as registration.js
function togglePasswordVisibility(inputId, toggleButton) {
  const input = $(`#${inputId}`);
  const icon = toggleButton.find('i');
  
  if (input.attr('type') === 'password') {
    input.attr('type', 'text');
    icon.removeClass('fa-eye').addClass('fa-eye-slash');
  } else {
    input.attr('type', 'password');
    icon.removeClass('fa-eye-slash').addClass('fa-eye');
  }
}

// Validation Functions
function validateNewPassword() {
  const password = newPasswordInput.val();
  const passwordError = $('#newPasswordError');
  
  if (!password) {
    passwordError.text('Password is required');
    newPasswordInput.addClass('is-invalid');
    return false;
  }
  
  if (!InputSanitizer.validatePassword(password)) {
    passwordError.text('Password must be at least 8 characters with uppercase, lowercase, number, and special character');
    newPasswordInput.addClass('is-invalid');
    return false;
  }
  
  passwordError.text('');
  newPasswordInput.removeClass('is-invalid');
  return true;
}

function validateConfirmPassword() {
  const password = newPasswordInput.val();
  const confirmPassword = confirmPasswordInput.val();
  const confirmPasswordError = $('#confirmPasswordError');
  
  if (!confirmPassword) {
    confirmPasswordError.text('Please confirm your password');
    confirmPasswordInput.addClass('is-invalid');
    return false;
  }
  
  if (password !== confirmPassword) {
    confirmPasswordError.text('Passwords do not match');
    confirmPasswordInput.addClass('is-invalid');
    return false;
  }
  
  confirmPasswordError.text('');
  confirmPasswordInput.removeClass('is-invalid');
  return true;
}

function validateVerificationCode() {
  const verificationCode = verificationCodeInput.val();
  const verificationCodeError = $('#verificationCodeError');

  if (!verificationCode) {
    verificationCodeError.text('Verification code is required');
    verificationCodeInput.addClass('is-invalid');
    return false;
  }

  if (!/^\d{6}$/.test(verificationCode)) {
    verificationCodeError.text('Verification code must be exactly 6 digits');
    verificationCodeInput.addClass('is-invalid');
    return false;
  }

  verificationCodeError.text('');
  verificationCodeInput.removeClass('is-invalid');
  return true;
}

function validateForm() {
  // This page only handles password reset with verification code
  const isVerificationCodeValid = validateVerificationCode();
  const isNewPasswordValid = validateNewPassword();
  const isConfirmPasswordValid = validateConfirmPassword();

  return isVerificationCodeValid && isNewPasswordValid && isConfirmPasswordValid;
}

// Password Reset Operation Function (only handles token-based reset with verification code)
async function performPasswordReset(newPassword, verificationCode) {
  try {
    // Sanitize inputs
    const sanitizedPassword = InputSanitizer.sanitizeInput(newPassword);
    const sanitizedVerificationCode = InputSanitizer.sanitizeInput(verificationCode);

    if (!InputSanitizer.validatePassword(sanitizedPassword)) {
      throw new Error('Invalid password format');
    }

    if (!RESET_TOKEN) {
      throw new Error('Reset token is missing');
    }

    if (!sanitizedVerificationCode) {
      throw new Error('Verification code is required');
    }

    // Build request body for token-based password reset
    const requestBody = {
      token: RESET_TOKEN,
      verificationCode: sanitizedVerificationCode,
      newPassword: sanitizedPassword,
      applicationName: APPLICATION_NAME
    };

    // Call PasswordService with unified operation
    const apiUrl = SecureConfig.buildSecureUrl('PasswordService', 'reset-complete');
    if (!apiUrl) {
      throw new Error('Azure Function configuration missing. Please check Power Pages settings.');
    }

    console.log('🚀 Password Reset API Request:', {
      url: apiUrl,
      method: 'POST',
      tokenPresent: !!RESET_TOKEN,
      tokenLength: RESET_TOKEN ? RESET_TOKEN.length : 0,
      verificationCodePresent: !!sanitizedVerificationCode,
      applicationName: APPLICATION_NAME,
      bodyStructure: {
        token: RESET_TOKEN ? 'Present' : 'Missing',
        verificationCode: sanitizedVerificationCode ? 'Present' : 'Missing',
        newPassword: sanitizedPassword ? 'Present' : 'Missing',
        applicationName: APPLICATION_NAME || 'Missing'
      }
    });

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Version': '3.0.0-simplified',
        'X-Requested-With': 'XMLHttpRequest' // CSRF protection
      },
      body: JSON.stringify(requestBody)
    });

    console.log('API Response Status:', response.status, response.statusText);

    // Handle rate limiting from server
    if (response.status === 429) {
      const responseText = await response.text();
      let result;
      try {
        result = JSON.parse(responseText);
        if (result.retryAfter) {
          const retryAfter = new Date(result.retryAfter);
          const waitTime = Math.ceil((retryAfter - new Date()) / 1000);
          throw new Error(`Too many requests. Please wait ${waitTime} seconds before trying again.`);
        }
      } catch (parseError) {
        // Fallback if we can't parse the response
        throw new Error('Too many requests. Please wait a moment before trying again.');
      }
      throw new Error('Too many requests. Please wait a moment before trying again.');
    }

    // Check if response has content before trying to parse JSON
    const responseText = await response.text();
    console.log('API Response Text:', responseText);

    let result;
    if (responseText) {
      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        console.error('JSON Parse Error:', parseError);
        throw new Error('Invalid response from server. Please try again.');
      }
    } else {
      throw new Error('Empty response from server. Please try again.');
    }

    if (response.ok) {
      // Handle both direct response format and data wrapper format
      const responseData = result.data || result;
      return {
        success: true,
        message: responseData.message || result.message || "Password updated successfully!",
        email: responseData.email || result.email,
        requiresLogout: responseData.requiresLogout || result.requiresLogout
      };
    } else {
      // Handle error response format
      const errorMessage = result.message || (result.data && result.data.message) || "Password operation failed. Please try again.";
      throw new Error(errorMessage);
    }

  } catch (error) {
    console.error("Password operation error:", error);
    throw error;
  }
}

// Initialize form handlers for password reset
function initializeFormHandlers() {
  // This page only handles password reset - no user email validation needed

  // Verification code validation
  verificationCodeInput.blur(function() {
    validateVerificationCode();
  });

  verificationCodeInput.on('input', function() {
    $(this).removeClass('is-invalid');
    $('#verificationCodeError').text('');
  });





  newPasswordInput.blur(function() {
    validateNewPassword();
  });

  confirmPasswordInput.blur(function() {
    validateConfirmPassword();
  });

  newPasswordInput.on('input', function() {
    $(this).removeClass('is-invalid');
    $('#newPasswordError').text('');
  });

  confirmPasswordInput.on('input', function() {
    $(this).removeClass('is-invalid');
    $('#confirmPasswordError').text('');
  });

  toggleButtons.newPassword.click(function() {
    togglePasswordVisibility('newPassword', $(this));
  });

  toggleButtons.confirmPassword.click(function() {
    togglePasswordVisibility('confirmPassword', $(this));
  });

  // Form submit handler
  passwordForm.submit(async function(event) {
    event.preventDefault();
    clearMessages();

    try {
      // Client-side validation
      if (!validateForm()) {
        return;
      }

      // Disable button and show loading
      submitButton.prop("disabled", true);
      submitButton.text("Resetting...");

      // Get form values
      const newPassword = newPasswordInput.val();
      const verificationCode = verificationCodeInput.val();

      // Update loading message
      showMessage("Resetting your password...", false);

      // Perform password reset
      const result = await performPasswordReset(newPassword, verificationCode);

      if (result.success) {
        // Show success message
        showMessage("Password reset successfully!", false);
        passwordForm[0].reset();

        // Clear any validation states
        $('input').removeClass('is-invalid');
        $('.invalid-feedback').text('');

        // Show success notification popup
        showSuccessNotification("Password Reset Successful!", "Your password has been reset successfully. You can now log in with your new password.");

        // Redirect after showing notification
        setTimeout(() => {
          window.location.href = '/?message=' + encodeURIComponent('Password reset successfully. You can now log in with your new password.');
        }, 3000); // Increased delay to show notification
      }

    } catch (error) {
      console.error("Form submission error:", error);
      showMessage(error.message || "An unexpected error occurred. Please try again.", true);
    } finally {
      // Re-enable button
      submitButton.prop("disabled", false);
      submitButton.text("Reset Password");
    }
  });
}

// Initialize form for password reset only
function initializeResetForm() {
  // This page only handles password reset with verification code
  console.log('Initialized for password reset with token:', RESET_TOKEN);

  // Set button text
  submitButton.text('Reset Password');
}

// Initialize the application when the document is ready
$(document).ready(function() {
  initializeResetForm();
  initializeFormHandlers();
});
