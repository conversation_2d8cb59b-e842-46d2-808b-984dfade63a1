using System.Security.Cryptography;

namespace PasswordHistoryValidator.Shared;

/// <summary>
/// Utility class for generating secure tokens and verification codes
/// </summary>
public static class TokenHelper
{
    /// <summary>
    /// Generates a cryptographically secure 6-digit verification code
    /// Used for email verification in password reset and invitation flows
    /// </summary>
    public static string GenerateVerificationCode()
    {
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[4];
        rng.GetBytes(bytes);
        var randomNumber = BitConverter.ToUInt32(bytes, 0);
        var code = (randomNumber % 900000) + 100000; // Ensures 6-digit code (100000-999999)
        return code.ToString();
    }
}