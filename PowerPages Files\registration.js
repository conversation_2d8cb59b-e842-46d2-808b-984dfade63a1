// Power Pages registration form handler
// Manages user registration with invitation validation and password history integration

const SecureConfig = {
  // Constructs Azure Function URL from Power Pages configuration
  getFunctionUrl(functionName = 'AuthenticationService') {
    const baseUrl = window.appConfig?.functionUrl;
    if (!baseUrl) {
      return null;
    }
    return `${baseUrl}/api/${functionName}`;
  },

  // Retrieves appropriate function key based on service type
  getFunctionKey(functionName) {
    let functionKey;

    // Map function names to their respective keys from Power Pages config
    switch(functionName) {
      case 'InvitationService':
        functionKey = window.appConfig?.invitationFunctionKey;
        break;
      case 'RegistrationService':
        functionKey = window.appConfig?.registrationFunctionKey;
        break;
      case 'AuthenticationService':
        functionKey = window.appConfig?.authenticationFunctionKey;
        break;
      case 'PasswordService':
        functionKey = window.appConfig?.passwordFunctionKey;
        break;
      default:
        functionKey = window.appConfig?.registrationFunctionKey; // Default for registration page
    }

    if (!functionKey || functionKey.includes('ERROR_MISSING')) {
      return null;
    }
    return functionKey;
  },

  buildSecureUrl(functionName, operation) {
    const baseUrl = this.getFunctionUrl(functionName);
    const functionKey = this.getFunctionKey(functionName);
    if (!baseUrl || !functionKey) {
      return null;
    }
    return `${baseUrl}?operation=${operation}&code=${functionKey}`;
  },

  getMSALConfig() {
    return {
      clientId: window.appConfig?.msalClientId,
      tenantId: window.appConfig?.msalTenantId
    };
  }
};

const AZURE_FUNCTION_URL = SecureConfig.getFunctionUrl('AuthenticationService');
const msalConfigData = SecureConfig.getMSALConfig();
const MSAL_CLIENT_ID = msalConfigData.clientId;
const MSAL_TENANT_ID = msalConfigData.tenantId;
const MSAL_REDIRECT_URI = window.location.origin;

const APPLICATION_NAME = window.appConfig?.applicationName || "ApplicationNameNotSet";

const InputSanitizer = {
  sanitizeString(input) {
    if (typeof input !== 'string') return '';
    return input.trim().replace(/[<>\"'&]/g, '').substring(0, 256);
  },

  validatePassword(password) {
    if (!password || typeof password !== 'string') return false;
    if (password.length < 8 || password.length > 128) return false;
    return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]).{8,}$/.test(password);
  },

  validateEmail(email) {
    if (!email || typeof email !== 'string') return false;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
  },

  validateName(name) {
    if (!name || typeof name !== 'string') return false;
    const trimmed = name.trim();
    return trimmed.length >= 1 && trimmed.length <= 50 && /^[a-zA-Z\s'-]+$/.test(trimmed);
  }
};

const errorMessageDiv = $('#errorMessage');
const successMessageDiv = $('#successMessage');
const registerButton = $('#registerButton');
const registrationForm = $('#registrationForm');
const registrationFieldsDiv = $('#registrationFields');
const toggleButtons = {
  password: $('#togglePassword'),
  confirm: $('#toggleConfirmPassword')
};

const msalConfig = {
  auth: {
    clientId: MSAL_CLIENT_ID,
    authority: `https://login.microsoftonline.com/${MSAL_TENANT_ID}`,
    redirectUri: MSAL_REDIRECT_URI
  },
  cache: {
    cacheLocation: "sessionStorage",
    storeAuthStateInCookie: false
  }
};

const msalInstance = new msal.PublicClientApplication(msalConfig);

msalInstance.handleRedirectPromise()
  .then(tokenResponse => {
    if (tokenResponse) {
      msalInstance.setActiveAccount(tokenResponse.account);
    }
  })
  .catch(error => {
    console.error("MSAL error:", error);
    showMessage("Authentication error");
  });

function showMessage(message, isError = true, timeout = 0) {
  errorMessageDiv.addClass('d-none');
  successMessageDiv.addClass('d-none');
  const messageDiv = isError ? errorMessageDiv : successMessageDiv;
  messageDiv.html(message).removeClass('d-none');
  if (timeout > 0) {
    setTimeout(() => messageDiv.addClass('d-none'), timeout);
  }
}

function showLoadingState(message) {
  registerButton.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> ' + message);
}

function resetLoadingState() {
  registerButton.prop('disabled', false).text('Create Account');
}

function validatePasswordComplexity(password) {
  if (!password || password.length < 8) return false;
  return /[A-Z]/.test(password) &&
         /[a-z]/.test(password) &&
         /\d/.test(password) &&
         /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/.test(password);
}

function togglePasswordVisibility(inputId, toggleButton) {
  const input = $(`#${inputId}`);
  const icon = toggleButton.find('i');
  
  if (input.attr('type') === 'password') {
    input.attr('type', 'text');
    icon.removeClass('fa-eye').addClass('fa-eye-slash');
  } else {
    input.attr('type', 'password');
    icon.removeClass('fa-eye-slash').addClass('fa-eye');
  }
}

// Core Registration Function
async function registerUser(userData) {
  showLoadingState('Creating Account...');
  try {
    // Input sanitization and validation
    const sanitizedData = {
      email: InputSanitizer.sanitizeString(userData.email),
      password: InputSanitizer.sanitizeString(userData.password),
      firstName: InputSanitizer.sanitizeString(userData.firstName),
      lastName: InputSanitizer.sanitizeString(userData.lastName),
      invitationCode: InputSanitizer.sanitizeString(userData.invitationCode || '')
    };

    if (!InputSanitizer.validateEmail(sanitizedData.email)) {
      throw new Error('Invalid email format');
    }

    if (!InputSanitizer.validatePassword(sanitizedData.password)) {
      throw new Error('Password does not meet security requirements');
    }

    if (!InputSanitizer.validateName(sanitizedData.firstName)) {
      throw new Error('Invalid first name');
    }

    if (!InputSanitizer.validateName(sanitizedData.lastName)) {
      throw new Error('Invalid last name');
    }

    if (!sanitizedData.invitationCode || sanitizedData.invitationCode.length < 6) {
      throw new Error('Valid invitation code is required');
    }

    // Use RegistrationService for invitation-based registration
    const secureUrl = SecureConfig.buildSecureUrl('RegistrationService', 'register');
    if (!secureUrl) {
      throw new Error('Azure Function configuration missing. Please check Power Pages settings.');
    }

    const response = await fetch(secureUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Version': '3.0.0-simplified',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify({
        Email: sanitizedData.email,
        Password: sanitizedData.password,
        FirstName: sanitizedData.firstName,
        LastName: sanitizedData.lastName,
        ApplicationName: APPLICATION_NAME,
        Token: invitationToken,
        VerificationCode: sanitizedData.invitationCode
      })
    });

    // Handle rate limiting from server
    if (response.status === 429) {
      const responseText = await response.text();
      let result;
      try {
        result = JSON.parse(responseText);
        if (result.retryAfter) {
          const retryAfter = new Date(result.retryAfter);
          const waitTime = Math.ceil((retryAfter - new Date()) / 1000);
          throw new Error(`Too many requests. Please wait ${waitTime} seconds before trying again.`);
        }
      } catch (parseError) {
        // Fallback if we can't parse the response
        throw new Error('Too many requests. Please wait a moment before trying again.');
      }
      throw new Error('Too many requests. Please wait a moment before trying again.');
    }

    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      const textResponse = await response.text();
      throw new Error(`Non-JSON response: ${response.status}. Response: ${textResponse.substring(0, 200)}`);
    }

    const result = await response.json();
    
    // Handle the response format from CreateJsonResponse (data is nested)
    const data = result.data || result;

    if (!response.ok) {
      throw new Error(data.message || result.message || `Registration error ${response.status}`);
    }

    if (data.success === false) {
      throw new Error(data.message || "Registration failed. Please try again.");
    }

    return { success: true, message: data.message || "Account created successfully!" };
  } catch (error) {
    throw error;
  } finally {
    resetLoadingState();
  }
}

// Client-side form validation
function validateRegistrationForm() {
  let isValid = true;
  
  // Clear previous validation states
  $('.form-control').removeClass('is-invalid');
  $('.invalid-feedback').text('');

  // Email validation
  const email = $('#email').val();
  if (!InputSanitizer.validateEmail(email)) {
    $('#emailError').text('Please enter a valid email address');
    $('#email').addClass('is-invalid');
    isValid = false;
  }

  // First name validation
  const firstName = $('#firstName').val();
  if (!InputSanitizer.validateName(firstName)) {
    $('#firstNameError').text('Please enter a valid first name (letters, spaces, hyphens, and apostrophes only)');
    $('#firstName').addClass('is-invalid');
    isValid = false;
  }

  // Last name validation
  const lastName = $('#lastName').val();
  if (!InputSanitizer.validateName(lastName)) {
    $('#lastNameError').text('Please enter a valid last name (letters, spaces, hyphens, and apostrophes only)');
    $('#lastName').addClass('is-invalid');
    isValid = false;
  }

  // Password validation
  const password = $('#password').val();
  if (!InputSanitizer.validatePassword(password)) {
    $('#passwordError').text('Password must meet complexity requirements');
    $('#password').addClass('is-invalid');
    isValid = false;
  }

  // Confirm password validation
  const confirmPassword = $('#confirmPassword').val();
  if (password !== confirmPassword) {
    $('#confirmPasswordError').text('Passwords do not match');
    $('#confirmPassword').addClass('is-invalid');
    isValid = false;
  }

  // Terms acceptance validation
  const invitationCode = $('#invitationCode').val();
  if (!invitationCode || invitationCode.trim().length < 6) {
    $('#invitationCodeError').text('Please enter a valid invitation code');
    $('#invitationCode').addClass('is-invalid');
    isValid = false;
  }

  const termsAccepted = $('#termsAccepted').is(':checked');
  if (!termsAccepted) {
    $('#termsError').text('You must accept the terms and conditions');
    $('#termsAccepted').addClass('is-invalid');
    isValid = false;
  }

  return isValid;
}

// Event Handlers
function initializeFormHandlers() {
  // Password visibility toggles
  if (toggleButtons.password && toggleButtons.password.length) {
    toggleButtons.password.click(() => togglePasswordVisibility('password', toggleButtons.password));
  }
  if (toggleButtons.confirm && toggleButtons.confirm.length) {
    toggleButtons.confirm.click(() => togglePasswordVisibility('confirmPassword', toggleButtons.confirm));
  }

  // Form submit handler
  registrationForm.submit(async function(event) {
    event.preventDefault();
    showMessage('', false); // Clear previous messages

    try {
      // Client-side validation
      if (!validateRegistrationForm()) {
        return;
      }

      // Collect form data
      const userData = {
        email: $('#email').val(),
        password: $('#password').val(),
        firstName: $('#firstName').val(),
        lastName: $('#lastName').val(),
        invitationCode: $('#invitationCode').val()
      };

      // Register user
      const result = await registerUser(userData);
      
      if (result.success) {
        showMessage("Account created successfully! Redirecting to home page...", false);
        registrationForm[0].reset();

        setTimeout(() => {
          sessionStorage.setItem('registeredEmail', userData.email);
          sessionStorage.setItem('justRegistered', 'true');
          window.location.href = '/';
        }, 2000);
      }

    } catch (error) {
      showMessage(error.message || "An unexpected error occurred. Please try again.");
    }
  });
}

// Enhanced Security: Validate token for page access and require manual code entry
let invitationToken = null;

function validateInvitationTokenAndExtractCode() {
  const urlParams = new URLSearchParams(window.location.search);
  invitationToken = urlParams.get('token');
  const invitationCode = urlParams.get('code');
  
  // First, validate the token for page access
  if (!invitationToken) {
    showUnauthorizedAccess('No invitation token provided. Please use the link from your invitation email.');
    return;
  }
  
  // Validate token with server
  validateTokenAccess(invitationToken);
  
  if (invitationCode) {
    displayCodeReference(invitationCode);
  }
}

// Validate token access with server
async function validateTokenAccess(token) {
  try {
    const secureUrl = SecureConfig.buildSecureUrl('InvitationService', 'validate-token');
    if (!secureUrl) {
      throw new Error('Azure Function configuration missing. Please check Power Pages settings.');
    }

    const response = await fetch(secureUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: token
      })
    });

    const result = await response.json();
    const data = result.data || result;

    if (!data.success) {
      showUnauthorizedAccess(data.message || 'Invalid or expired invitation token.');
      return;
    }

    showTokenValidationSuccess();

  } catch (error) {
    showUnauthorizedAccess('Unable to validate invitation token. Please try again.');
  }
}

// Display verification code for user reference (don't auto-fill)
function displayCodeReference(code) {
  const codeDisplay = document.createElement('div');
  codeDisplay.className = 'alert alert-info mt-3';
  codeDisplay.innerHTML = `
    <h6><i class="fas fa-info-circle"></i> Your Verification Code</h6>
    <p class="mb-2">Enter this code in the form below:</p>
    <div class="code-display" style="font-family: monospace; font-size: 1.2em; font-weight: bold; color: #0066cc;">${code}</div>
    <small class="text-muted">This code was provided in your invitation email.</small>
  `;
  
  // Insert before the form
  const form = document.getElementById('registrationForm');
  if (form && form.parentNode) {
    form.parentNode.insertBefore(codeDisplay, form);
  }
}

// Show unauthorized access - redirect to error page
function showUnauthorizedAccess(message) {
  console.error('Access denied:', message);
  
  // Store error details for the error page
  sessionStorage.setItem('invitationError', JSON.stringify({
    message: message,
    timestamp: new Date().toISOString(),
    source: 'registration-token-validation'
  }));
  
  // Redirect to error page (or home page if no error page exists)
  // You can create a dedicated error page in Power Pages for better UX
  const errorPageUrl = '/Invitation-Error/'; // Change this to your error page URL
  
  // Try to redirect to error page, fallback to home
  try {
    window.location.href = errorPageUrl;
  } catch (error) {
    console.warn('Error page not found, redirecting to home:', error);
    window.location.href = '/';
  }
}

// Show token validation success
function showTokenValidationSuccess() {
  const pageHeader = document.querySelector('.card-header h3');
  if (pageHeader) {
    pageHeader.innerHTML = '<i class="fas fa-check-circle text-success"></i> Create Account <small class="text-success">(Invitation Verified)</small>';
  }
}

// Initialize the application when the document is ready
$(document).ready(function() {
  initializeFormHandlers();
  validateInvitationTokenAndExtractCode();
});
