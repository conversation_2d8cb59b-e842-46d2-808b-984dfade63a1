<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="power-pages-styles.css">
<link rel="stylesheet" href="send-invitation.css">

    <meta name="azure-function-url" content="{{ settings['AzureFunctionUrl'] | default: '' }}">
    <meta name="invitation-function-key" content="{{ settings['Invitation Function Key'] | default: '' }}">
    <meta name="application-name" content="{{ settings['ApplicationName'] | default: '' }}">

    <script>
      function getConfig() {
        const functionUrl = document.querySelector('meta[name="azure-function-url"]')?.content;
        const invitationFunctionKey = document.querySelector('meta[name="invitation-function-key"]')?.content;
        const applicationName = document.querySelector('meta[name="application-name"]')?.content;

        return {
          functionUrl: functionUrl || null,
          invitationFunctionKey: invitationFunctionKey || null,
          applicationName: applicationName || null
        };
      }

      window.appConfig = getConfig();
    </script>

<div class="col-lg-12 columnBlockLayout">
<div class="row sectionBlockLayout text-start">
  <div class="container container-flex">
    <div class="container send-invitation-container">
      <h2>Send User Invitation</h2>
      <p>Send an invitation to a new user to join the application.</p>
      <div id="errorMessage" class="alert alert-danger d-none"></div>
      <div id="successMessage" class="alert alert-success d-none"></div>

      <!-- Invitation Form -->
      <form id="invitationForm">
        <!-- Email Field -->
        <div class="form-group mb-3">
          <label for="email" class="form-label fw-bold">Email Address</label>
          <input type="email" class="form-control" id="email" required maxlength="256"
                 placeholder="Enter email address">
          <div class="form-text">The user will receive an invitation email at this address</div>
          <div class="invalid-feedback"></div>
        </div>

        <!-- First Name -->
        <div class="form-group mb-3">
          <label for="firstName" class="form-label fw-bold">First Name</label>
          <input type="text" class="form-control" id="firstName" required maxlength="50"
                 placeholder="Enter first name">
          <div class="invalid-feedback"></div>
        </div>

        <!-- Last Name -->
        <div class="form-group mb-3">
          <label for="lastName" class="form-label fw-bold">Last Name</label>
          <input type="text" class="form-control" id="lastName" required maxlength="50"
                 placeholder="Enter last name">
          <div class="invalid-feedback"></div>
        </div>

        <button type="submit" class="btn btn-primary" id="sendButton">
          <i class="fas fa-paper-plane me-2"></i>
          Send Invitation
        </button>
      </form>

      <!-- Recent Invitations -->
      <div class="mt-4 pt-3 border-top">
        <h6 class="text-muted mb-3">Recent Invitations</h6>
        <div id="recentInvitations" class="small text-muted">
          No recent invitations
        </div>
      </div>
    </div>
  </div>
</div>
</div>

