namespace PasswordHistoryValidator.Shared;

/// <summary>
/// Configuration options for SendGrid email service
/// </summary>
public class SendGridOptions
{
    public const string SectionName = "SendGrid";

    public string ApiKey { get; set; } = string.Empty;
    public string FromEmail { get; set; } = string.Empty;
    public string PasswordResetTemplateId { get; set; } = string.Empty;
    public string PasswordChangedTemplateId { get; set; } = string.Empty;
    public string UserInvitationTemplateId { get; set; } = string.Empty;
    public string AccountCreatedTemplateId { get; set; } = string.Empty;
}

/// <summary>
/// Configuration options for Entra External ID integration
/// </summary>
public class EntraOptions
{
    public const string SectionName = "EntraExternalID";

    public string TenantId { get; set; } = string.Empty;
    public string ClientId { get; set; } = string.Empty;
    public string ClientSecret { get; set; } = string.Empty;

    public string DefaultDomain { get; set; } = "yourtenant.onmicrosoft.com";
}

/// <summary>
/// Configuration options for password reset functionality
/// </summary>
public class PasswordResetOptions
{
    public const string SectionName = "PasswordReset";

    public string BaseUrl { get; set; } = string.Empty;
}

/// <summary>
/// Configuration options for account registration functionality
/// </summary>
public class AccountRegistrationOptions
{
    public const string SectionName = "AccountRegistration";

    public string BaseUrl { get; set; } = string.Empty;
}

/// <summary>
/// Configuration options for rate limiting
/// </summary>
public class RateLimitOptions
{
    public const string SectionName = "RateLimit";

    public int MaxRequestsPerMinute { get; set; } = 60;
}


public class StorageOptions
{
    public const string SectionName = "Storage";

    public string ConnectionString { get; set; } = string.Empty;
}


public class InvitationOptions
{
    public const string SectionName = "Invitation";

    public int TokenExpirationDays { get; set; } = 45;
}
