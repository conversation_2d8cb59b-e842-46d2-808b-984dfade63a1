using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Text.Json;
using PasswordHistoryValidator.Services;

namespace PasswordHistoryValidator.Shared;

/// <summary>
/// Base class for Azure Functions providing common response handling and utilities
/// Standardizes JSON responses, CORS handling, and correlation tracking
/// </summary>
public abstract class BaseFunctionService
{
    protected readonly JsonSerializerOptions JsonOptions;

    protected BaseFunctionService(JsonSerializerOptions jsonOptions)
    {
        JsonOptions = jsonOptions ?? throw new ArgumentNullException(nameof(jsonOptions));
    }

    #region Response Methods

    /// <summary>
    /// Creates standardized JSON response with correlation tracking
    /// </summary>
    protected async Task<HttpResponseData> CreateJsonResponse<T>(HttpRequestData req, T data, HttpStatusCode statusCode, string correlationId)
    {
        var response = req.CreateResponse(statusCode);
        var result = new
        {
            data = data,
            correlationId = correlationId,
            timestamp = DateTime.UtcNow
        };

        response.Headers.Add("Content-Type", "application/json");
        var json = JsonSerializer.Serialize(result, JsonOptions);
        await response.WriteStringAsync(json);
        AddCorsHeaders(response);
        return response;
    }

    /// <summary>
    /// Creates standardized error response with correlation tracking
    /// </summary>
    protected async Task<HttpResponseData> CreateErrorResponse(HttpRequestData req, string message, string correlationId, HttpStatusCode statusCode = HttpStatusCode.BadRequest)
    {
        var response = req.CreateResponse(statusCode);
        var result = new
        {
            success = false,
            message = message,
            correlationId = correlationId,
            timestamp = DateTime.UtcNow
        };

        response.Headers.Add("Content-Type", "application/json");
        var json = JsonSerializer.Serialize(result, JsonOptions);
        await response.WriteStringAsync(json);
        AddCorsHeaders(response);
        return response;
    }

    #endregion

    #region CORS and Common Utilities

    /// <summary>
    /// Adds CORS headers to response for Power Pages integration
    /// </summary>
    protected virtual void AddCorsHeaders(HttpResponseData response)
    {
        response.Headers.Add("Access-Control-Allow-Origin", "*");
        response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        response.Headers.Add("Access-Control-Allow-Headers", "Content-Type, Authorization, x-functions-key, X-Requested-With, X-Client-Version");
        response.Headers.Add("Access-Control-Max-Age", "86400");
    }

    /// <summary>
    /// Creates CORS preflight response for OPTIONS requests
    /// </summary>
    protected HttpResponseData CreateCorsResponse(HttpRequestData req)
    {
        var corsResponse = req.CreateResponse(HttpStatusCode.OK);
        AddCorsHeaders(corsResponse);
        return corsResponse;
    }

    /// <summary>
    /// Generates unique correlation ID for request tracking
    /// </summary>
    protected static string GenerateCorrelationId() => Guid.NewGuid().ToString();

    /// <summary>
    /// Extracts client identifier from request headers for rate limiting
    /// </summary>
    protected static string GetClientIdentifier(HttpRequestData req)
    {
        var clientIp = req.Headers.GetValues("X-Forwarded-For").FirstOrDefault()?.Split(',').FirstOrDefault()?.Trim()
                      ?? req.Headers.GetValues("X-Real-IP").FirstOrDefault()
                      ?? req.Headers.GetValues("CF-Connecting-IP").FirstOrDefault()
                      ?? "unknown";

        return $"ip:{clientIp}";
    }

    #endregion
}
